{"installationFolder": "C:\\Users\\<USER>\\Downloads\\tms_application\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "3.17.5", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "C:\\Users\\<USER>\\Downloads\\tms_application\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "x86_64", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "C:\\Users\\<USER>\\Downloads\\tms_application\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6u5ua03s\\obj\\x86_64\\libreanimated.so", "abiAndroidGradleBuildJsonFile": "C:\\Users\\<USER>\\Downloads\\tms_application\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6u5ua03s\\x86_64\\android_gradle_build.json"}]}, {"moduleName": "worklets", "moduleHeaders": "C:\\Users\\<USER>\\Downloads\\tms_application\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\worklets", "moduleExportLibraries": [], "abis": [{"abiName": "x86_64", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "C:\\Users\\<USER>\\Downloads\\tms_application\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\6u5ua03s\\obj\\x86_64\\libworklets.so", "abiAndroidGradleBuildJsonFile": "C:\\Users\\<USER>\\Downloads\\tms_application\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\6u5ua03s\\x86_64\\android_gradle_build.json"}]}]}}