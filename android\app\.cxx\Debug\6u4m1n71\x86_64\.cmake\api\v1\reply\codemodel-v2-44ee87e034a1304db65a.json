{"configurations": [{"directories": [{"build": ".", "childIndexes": [1, 2, 3, 4, 5, 6, 7, 8, 9], "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}, {"build": "rnasyncstorage_autolinked_build", "jsonFile": "directory-rnasyncstorage_autolinked_build-Debug-45ff86c19276f7554497.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni", "targetIndexes": [4]}, {"build": "RNDateTimePickerCGen_autolinked_build", "jsonFile": "directory-RNDateTimePickerCGen_autolinked_build-Debug-c96dabb047f5664b2e37.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni", "targetIndexes": [2]}, {"build": "rngesturehandler_codegen_autolinked_build", "jsonFile": "directory-rngesturehandler_codegen_autolinked_build-Debug-d8115f47f4343cc512d6.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni", "targetIndexes": [5]}, {"build": "rnreanimated_autolinked_build", "jsonFile": "directory-rnreanimated_autolinked_build-Debug-fab031e532ec35d119d5.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni", "targetIndexes": [6]}, {"build": "safeareacontext_autolinked_build", "jsonFile": "directory-safeareacontext_autolinked_build-Debug-b8913d92b10f1faa9907.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni", "targetIndexes": [9]}, {"build": "rnscreens_autolinked_build", "jsonFile": "directory-rnscreens_autolinked_build-Debug-16c4eaa20deb2cef32a6.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni", "targetIndexes": [7]}, {"build": "rnsvg_autolinked_build", "jsonFile": "directory-rnsvg_autolinked_build-Debug-49fe0ed58e6d0cf729ef.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni", "targetIndexes": [8]}, {"build": "RNCWebViewSpec_autolinked_build", "jsonFile": "directory-RNCWebViewSpec_autolinked_build-Debug-fa8c00ecb3aff3ac97c7.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni", "targetIndexes": [1]}, {"build": "RNEdgeToEdge_autolinked_build", "jsonFile": "directory-RNEdgeToEdge_autolinked_build-Debug-3b114c504dad73716122.json", "minimumCMakeVersion": {"string": "3.13"}, "parentIndex": 0, "projectIndex": 0, "source": "C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni", "targetIndexes": [3]}], "name": "Debug", "projects": [{"directoryIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9], "name": "appmodules", "targetIndexes": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-0572a7d2055e3d0d4470.json", "name": "appmodules", "projectIndex": 0}, {"directoryIndex": 8, "id": "react_codegen_RNCWebViewSpec::@eb48929f9f7453740a6c", "jsonFile": "target-react_codegen_RNCWebViewSpec-Debug-5cfaa0fa7354358cf8bb.json", "name": "react_codegen_RNCWebViewSpec", "projectIndex": 0}, {"directoryIndex": 2, "id": "react_codegen_RNDateTimePickerCGen::@59b70ddc31ba2f8ef1d2", "jsonFile": "target-react_codegen_RNDateTimePickerCGen-Debug-1723d3d0b00e4603e0ca.json", "name": "react_codegen_RNDateTimePickerCGen", "projectIndex": 0}, {"directoryIndex": 9, "id": "react_codegen_RNEdgeToEdge::@668218a48b60eb28aa9e", "jsonFile": "target-react_codegen_RNEdgeToEdge-Debug-94eaca67700be226fbf8.json", "name": "react_codegen_RNEdgeToEdge", "projectIndex": 0}, {"directoryIndex": 1, "id": "react_codegen_rnasyncstorage::@1596841e19ec5b9eeffe", "jsonFile": "target-react_codegen_rnasyncstorage-Debug-5495e673258b7cc32d0c.json", "name": "react_codegen_rnasyncstorage", "projectIndex": 0}, {"directoryIndex": 3, "id": "react_codegen_rngesturehandler_codegen::@39f233abcd2c728bc6ec", "jsonFile": "target-react_codegen_rngesturehandler_codegen-Debug-f7b7eb8a940818131999.json", "name": "react_codegen_rngesturehandler_codegen", "projectIndex": 0}, {"directoryIndex": 4, "id": "react_codegen_rnreanimated::@8afabad14bfffa3f8b9a", "jsonFile": "target-react_codegen_rnreanimated-Debug-3f2b96b514ee2bb281e9.json", "name": "react_codegen_rnreanimated", "projectIndex": 0}, {"directoryIndex": 6, "id": "react_codegen_rnscreens::@25bcbd507e98d3a854ad", "jsonFile": "target-react_codegen_rnscreens-Debug-716614cf14feba67cb67.json", "name": "react_codegen_rnscreens", "projectIndex": 0}, {"directoryIndex": 7, "id": "react_codegen_rnsvg::@4f40eb209d0c0b4a3b65", "jsonFile": "target-react_codegen_rnsvg-Debug-17c911cef65fc642b983.json", "name": "react_codegen_rnsvg", "projectIndex": 0}, {"directoryIndex": 5, "id": "react_codegen_safeareacontext::@7984cd80db47aa7b952a", "jsonFile": "target-react_codegen_safeareacontext-Debug-a8fdcfd8d3b8d88f537c.json", "name": "react_codegen_safeareacontext", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/x86_64", "source": "C:/Users/<USER>/Downloads/tms_application/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}