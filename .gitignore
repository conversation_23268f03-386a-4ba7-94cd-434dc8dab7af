# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# CNG/Prebuild - ignore native folders when using prebuild
# /android
# /ios

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env
.env*.local
.env.production
.env.staging

# Security - Never commit these files
*.key
*.pem
*.p12
*.p8
*.jks
*.keystore
config/secrets.json

# typescript
*.tsbuildinfo
