1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.ramsha_malik.tms_app"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:4:3-75
11-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:4:20-73
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:2:3-64
12-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:2:20-62
13    <uses-permission
13-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:3:3-77
14        android:name="android.permission.READ_EXTERNAL_STORAGE"
14-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:3:20-75
15        android:maxSdkVersion="32" />
15-->[BareExpo:expo.modules.image:2.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\cda720c6dedd46fb95f91f25b4ce05a7\transformed\expo.modules.image-2.3.0\AndroidManifest.xml:17:9-35
16    <uses-permission android:name="android.permission.VIBRATE" />
16-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:5:3-63
16-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:5:20-61
17    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
17-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:6:3-78
17-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:6:20-76
18
19    <queries>
19-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:7:3-13:13
20        <intent>
20-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:8:5-12:14
21            <action android:name="android.intent.action.VIEW" />
21-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:9:7-58
21-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:9:15-56
22
23            <category android:name="android.intent.category.BROWSABLE" />
23-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:10:7-67
23-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:10:17-65
24
25            <data android:scheme="https" />
25-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:11:7-37
25-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:11:13-35
26        </intent>
27        <!-- Query open documents -->
28        <intent>
28-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-17:18
29            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
29-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-79
29-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:21-76
30        </intent>
31        <intent>
31-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\99eeaa698886199b824f04db24c6bff1\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:8:9-14:18
32
33            <!-- Required for file sharing if targeting API 30 -->
34            <action android:name="android.intent.action.SEND" />
34-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\99eeaa698886199b824f04db24c6bff1\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:13-65
34-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\99eeaa698886199b824f04db24c6bff1\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:11:21-62
35
36            <data android:mimeType="*/*" />
36-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:11:7-37
37        </intent>
38    </queries>
39
40    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
40-->[:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
40-->[:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
41    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
41-->[:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-76
41-->[:react-native-community_netinfo] C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-73
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.ramsha_malik.tms_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.ramsha_malik.tms_app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
49-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:3-32:17
50        android:name="com.ramsha_malik.tms_app.MainApplication"
50-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:16-47
51        android:allowBackup="true"
51-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:162-188
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
53        android:debuggable="true"
54        android:extractNativeLibs="false"
55        android:icon="@mipmap/ic_launcher"
55-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:81-115
56        android:label="@string/app_name"
56-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:48-80
57        android:roundIcon="@mipmap/ic_launcher_round"
57-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:116-161
58        android:supportsRtl="true"
58-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:221-247
59        android:theme="@style/AppTheme"
59-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:14:189-220
60        android:usesCleartextTraffic="true" >
60-->C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\AndroidManifest.xml:6:18-53
61        <meta-data
61-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:15:5-82
62            android:name="expo.modules.updates.ENABLED"
62-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:15:16-59
63            android:value="true" />
63-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:15:60-80
64        <meta-data
64-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:16:5-119
65            android:name="expo.modules.updates.EXPO_RUNTIME_VERSION"
65-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:16:16-72
66            android:value="@string/expo_runtime_version" />
66-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:16:73-117
67        <meta-data
67-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:17:5-105
68            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
68-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:17:16-80
69            android:value="ALWAYS" />
69-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:17:81-103
70        <meta-data
70-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:18:5-99
71            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
71-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:18:16-79
72            android:value="0" />
72-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:18:80-97
73        <meta-data
73-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:19:5-141
74            android:name="expo.modules.updates.EXPO_UPDATE_URL"
74-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:19:16-67
75            android:value="https://u.expo.dev/aec7ddf4-9474-4131-9a9a-4b9309bf4176" />
75-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:19:68-139
76
77        <activity
77-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:5-31:16
78            android:name="com.ramsha_malik.tms_app.MainActivity"
78-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:15-43
79            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
79-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:44-134
80            android:exported="true"
80-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:256-279
81            android:launchMode="singleTask"
81-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:135-166
82            android:screenOrientation="portrait"
82-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:280-316
83            android:theme="@style/Theme.App.SplashScreen"
83-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:210-255
84            android:windowSoftInputMode="adjustResize" >
84-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:20:167-209
85            <intent-filter>
85-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:21:7-24:23
86                <action android:name="android.intent.action.MAIN" />
86-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:22:9-60
86-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:22:17-58
87
88                <category android:name="android.intent.category.LAUNCHER" />
88-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:23:9-68
88-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:23:19-66
89            </intent-filter>
90            <intent-filter>
90-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:25:7-30:23
91                <action android:name="android.intent.action.VIEW" />
91-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:9:7-58
91-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:9:15-56
92
93                <category android:name="android.intent.category.DEFAULT" />
93-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:27:9-67
93-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:27:19-65
94                <category android:name="android.intent.category.BROWSABLE" />
94-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:10:7-67
94-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:10:17-65
95
96                <data android:scheme="tmsapp" />
96-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:11:7-37
96-->C:\Users\<USER>\Downloads\tms_application\android\app\src\main\AndroidManifest.xml:11:13-35
97            </intent-filter>
98        </activity>
99
100        <provider
100-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
101            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
101-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
102            android:authorities="com.ramsha_malik.tms_app.fileprovider"
102-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
103            android:exported="false"
103-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
104            android:grantUriPermissions="true" >
104-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
105            <meta-data
105-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
106                android:name="android.support.FILE_PROVIDER_PATHS"
106-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
107                android:resource="@xml/file_provider_paths" />
107-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
108        </provider>
109
110        <meta-data
110-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
111            android:name="org.unimodules.core.AppLoader#react-native-headless"
111-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
112            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
112-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
113        <meta-data
113-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
114            android:name="com.facebook.soloader.enabled"
114-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
115            android:value="true" />
115-->[:expo-modules-core] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
116
117        <activity
117-->[com.facebook.react:react-android:0.79.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a38de6e1ef4b6972bc76438000ddaf4\transformed\react-android-0.79.3-debug\AndroidManifest.xml:19:9-21:40
118            android:name="com.facebook.react.devsupport.DevSettingsActivity"
118-->[com.facebook.react:react-android:0.79.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a38de6e1ef4b6972bc76438000ddaf4\transformed\react-android-0.79.3-debug\AndroidManifest.xml:20:13-77
119            android:exported="false" />
119-->[com.facebook.react:react-android:0.79.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\5a38de6e1ef4b6972bc76438000ddaf4\transformed\react-android-0.79.3-debug\AndroidManifest.xml:21:13-37
120
121        <provider
121-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:9-30:20
122            android:name="expo.modules.filesystem.FileSystemFileProvider"
122-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-74
123            android:authorities="com.ramsha_malik.tms_app.FileSystemFileProvider"
123-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:13-74
124            android:exported="false"
124-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-37
125            android:grantUriPermissions="true" >
125-->[:expo-file-system] C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-47
126            <meta-data
126-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
127                android:name="android.support.FILE_PROVIDER_PATHS"
127-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
128                android:resource="@xml/file_system_provider_paths" />
128-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
129        </provider>
130        <provider
130-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\99eeaa698886199b824f04db24c6bff1\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:18:9-26:20
131            android:name="expo.modules.sharing.SharingFileProvider"
131-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\99eeaa698886199b824f04db24c6bff1\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:19:13-68
132            android:authorities="com.ramsha_malik.tms_app.SharingFileProvider"
132-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\99eeaa698886199b824f04db24c6bff1\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:20:13-71
133            android:exported="false"
133-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\99eeaa698886199b824f04db24c6bff1\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:21:13-37
134            android:grantUriPermissions="true" >
134-->[host.exp.exponent:expo.modules.sharing:13.1.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\99eeaa698886199b824f04db24c6bff1\transformed\expo.modules.sharing-13.1.5\AndroidManifest.xml:22:13-47
135            <meta-data
135-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
136                android:name="android.support.FILE_PROVIDER_PATHS"
136-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
137                android:resource="@xml/sharing_provider_paths" />
137-->[:react-native-webview] C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
138        </provider>
139
140        <meta-data
140-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
141            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
141-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
142            android:value="GlideModule" />
142-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\93d2a0f7a9b11b7e215314b344039690\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
143
144        <provider
144-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
145            android:name="androidx.startup.InitializationProvider"
145-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
146            android:authorities="com.ramsha_malik.tms_app.androidx-startup"
146-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
147            android:exported="false" >
147-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
148            <meta-data
148-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
149                android:name="androidx.emoji2.text.EmojiCompatInitializer"
149-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
150                android:value="androidx.startup" />
150-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
151            <meta-data
151-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
152                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
152-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
153                android:value="androidx.startup" />
153-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
154            <meta-data
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
155                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
156                android:value="androidx.startup" />
156-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
157        </provider>
158
159        <service
159-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
160            android:name="androidx.room.MultiInstanceInvalidationService"
160-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
161            android:directBootAware="true"
161-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
162            android:exported="false" />
162-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
163
164        <receiver
164-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
165            android:name="androidx.profileinstaller.ProfileInstallReceiver"
165-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
166            android:directBootAware="false"
166-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
167            android:enabled="true"
167-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
168            android:exported="true"
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
169            android:permission="android.permission.DUMP" >
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
170            <intent-filter>
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
171                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
171-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
172            </intent-filter>
173            <intent-filter>
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
174                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
175            </intent-filter>
176            <intent-filter>
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
177                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
178            </intent-filter>
179            <intent-filter>
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
180                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
181            </intent-filter>
182        </receiver>
183    </application>
184
185</manifest>
