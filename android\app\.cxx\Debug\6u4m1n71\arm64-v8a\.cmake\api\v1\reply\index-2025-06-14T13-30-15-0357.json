{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/AndroidSdk/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/AndroidSdk/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/AndroidSdk/cmake/3.22.1/bin/ctest.exe", "root": "D:/AndroidSdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-648991a43efb1feebd28.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-b6b39c4e5fd6b20d08f8.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-d8923b165b795f93fd7b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-b6b39c4e5fd6b20d08f8.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-d8923b165b795f93fd7b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-648991a43efb1feebd28.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}