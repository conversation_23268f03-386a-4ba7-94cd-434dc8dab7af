# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.22

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: appmodules
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.8


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/
# =============================================================================
# Object build statements for SHARED_LIBRARY target appmodules


#############################################
# Order-only phony target for appmodules

build cmake_object_order_depends_target_appmodules: phony || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen cmake_object_order_depends_target_react_codegen_RNEdgeToEdge cmake_object_order_depends_target_react_codegen_rnasyncstorage cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen cmake_object_order_depends_target_react_codegen_rnreanimated cmake_object_order_depends_target_react_codegen_rnscreens cmake_object_order_depends_target_react_codegen_rnsvg cmake_object_order_depends_target_react_codegen_safeareacontext

build CMakeFiles/appmodules.dir/C_/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o: CXX_COMPILER__appmodules_Debug C$:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\C_\Users\Ramsha_Malik\Downloads\tms_application\android\app\build\generated\autolinking\src\main\jni\autolinking.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -I"C:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir\C_\Users\Ramsha_Malik\Downloads\tms_application\android\app\build\generated\autolinking\src\main\jni
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libappmodules.pdb"

build CMakeFiles/appmodules.dir/OnLoad.cpp.o: CXX_COMPILER__appmodules_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp || cmake_object_order_depends_target_appmodules
  DEFINES = -Dappmodules_EXPORTS
  DEP_FILE = CMakeFiles\appmodules.dir\OnLoad.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup" -I"C:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = CMakeFiles\appmodules.dir
  OBJECT_FILE_DIR = CMakeFiles\appmodules.dir
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libappmodules.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target appmodules


#############################################
# Link the shared library C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libappmodules.so

build C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libappmodules.so: CXX_SHARED_LIBRARY_LINKER__appmodules_Debug rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ComponentDescriptors.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o CMakeFiles/appmodules.dir/C_/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o CMakeFiles/appmodules.dir/OnLoad.cpp.o | C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_safeareacontext.so C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnscreens.so C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnsvg.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so || C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnscreens.so C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnsvg.so C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_safeareacontext.so RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec RNDateTimePickerCGen_autolinked_build/react_codegen_RNDateTimePickerCGen RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen rnreanimated_autolinked_build/react_codegen_rnreanimated
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "C:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_safeareacontext.so"  "C:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnscreens.so"  "C:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnsvg.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"  -latomic -lm
  OBJECT_DIR = CMakeFiles\appmodules.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libappmodules.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = CMakeFiles\appmodules.dir\
  TARGET_FILE = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libappmodules.so"
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libappmodules.pdb"
  RSP_FILE = CMakeFiles\appmodules.rsp


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Downloads\tms_application\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnasyncstorage


#############################################
# Order-only phony target for react_codegen_rnasyncstorage

build cmake_object_order_depends_target_react_codegen_rnasyncstorage: phony || rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/Props.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/States.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage\rnasyncstorageJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\react\renderer\components\rnasyncstorage
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""

build rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o: CXX_COMPILER__react_codegen_rnasyncstorage_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/rnasyncstorage-generated.cpp || cmake_object_order_depends_target_react_codegen_rnasyncstorage
  DEP_FILE = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\rnasyncstorage-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  OBJECT_FILE_DIR = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir
  TARGET_COMPILE_PDB = rnasyncstorage_autolinked_build\CMakeFiles\react_codegen_rnasyncstorage.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnasyncstorage

build rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ComponentDescriptors.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/EventEmitters.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/Props.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/ShadowNodes.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/States.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/react/renderer/components/rnasyncstorage/rnasyncstorageJSI-generated.cpp.o rnasyncstorage_autolinked_build/CMakeFiles/react_codegen_rnasyncstorage.dir/rnasyncstorage-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\rnasyncstorage_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnasyncstorage_autolinked_build/edit_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\rnasyncstorage_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Downloads\tms_application\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnasyncstorage_autolinked_build/rebuild_cache: phony rnasyncstorage_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNDateTimePickerCGen


#############################################
# Order-only phony target for react_codegen_RNDateTimePickerCGen

build cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen: phony || RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/RNDateTimePickerCGen-generated.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\RNDateTimePickerCGen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  TARGET_COMPILE_PDB = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\
  TARGET_PDB = ""

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\13e5db451150cc1628879224ca4ec527\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\13e5db451150cc1628879224ca4ec527
  TARGET_COMPILE_PDB = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\
  TARGET_PDB = ""

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen
  TARGET_COMPILE_PDB = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\
  TARGET_PDB = ""

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/Props.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen
  TARGET_COMPILE_PDB = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\
  TARGET_PDB = ""

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen\RNDateTimePickerCGenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen
  TARGET_COMPILE_PDB = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\
  TARGET_PDB = ""

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen
  TARGET_COMPILE_PDB = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\
  TARGET_PDB = ""

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o: CXX_COMPILER__react_codegen_RNDateTimePickerCGen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen/States.cpp || cmake_object_order_depends_target_react_codegen_RNDateTimePickerCGen
  DEP_FILE = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/react/renderer/components/RNDateTimePickerCGen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir
  OBJECT_FILE_DIR = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\react\renderer\components\RNDateTimePickerCGen
  TARGET_COMPILE_PDB = RNDateTimePickerCGen_autolinked_build\CMakeFiles\react_codegen_RNDateTimePickerCGen.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNDateTimePickerCGen

build RNDateTimePickerCGen_autolinked_build/react_codegen_RNDateTimePickerCGen: phony RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/RNDateTimePickerCGen-generated.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/13e5db451150cc1628879224ca4ec527/ComponentDescriptors.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/EventEmitters.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/Props.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/ShadowNodes.cpp.o RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/States.cpp.o


#############################################
# Utility command for edit_cache

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\RNDateTimePickerCGen_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNDateTimePickerCGen_autolinked_build/edit_cache: phony RNDateTimePickerCGen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNDateTimePickerCGen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\RNDateTimePickerCGen_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Downloads\tms_application\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNDateTimePickerCGen_autolinked_build/rebuild_cache: phony RNDateTimePickerCGen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rngesturehandler_codegen


#############################################
# Order-only phony target for react_codegen_rngesturehandler_codegen

build cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen: phony || rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/Props.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/States.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\bac033cd950586cef66695376748dd33
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen\rngesturehandler_codegenJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\react\renderer\components\rngesturehandler_codegen
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""

build rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o: CXX_COMPILER__react_codegen_rngesturehandler_codegen_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/rngesturehandler_codegen-generated.cpp || cmake_object_order_depends_target_react_codegen_rngesturehandler_codegen
  DEP_FILE = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\rngesturehandler_codegen-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  OBJECT_FILE_DIR = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir
  TARGET_COMPILE_PDB = rngesturehandler_codegen_autolinked_build\CMakeFiles\react_codegen_rngesturehandler_codegen.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rngesturehandler_codegen

build rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ComponentDescriptors.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/EventEmitters.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/Props.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/ShadowNodes.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/bac033cd950586cef66695376748dd33/States.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/rngesturehandler_codegen-generated.cpp.o


#############################################
# Utility command for edit_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\rngesturehandler_codegen_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rngesturehandler_codegen_autolinked_build/edit_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\rngesturehandler_codegen_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Downloads\tms_application\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rngesturehandler_codegen_autolinked_build/rebuild_cache: phony rngesturehandler_codegen_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_rnreanimated


#############################################
# Order-only phony target for react_codegen_rnreanimated

build cmake_object_order_depends_target_react_codegen_rnreanimated: phony || rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/Props.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/States.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated\rnreanimatedJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\react\renderer\components\rnreanimated
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""

build rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o: CXX_COMPILER__react_codegen_rnreanimated_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/rnreanimated-generated.cpp || cmake_object_order_depends_target_react_codegen_rnreanimated
  DEP_FILE = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\rnreanimated-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  OBJECT_FILE_DIR = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir
  TARGET_COMPILE_PDB = rnreanimated_autolinked_build\CMakeFiles\react_codegen_rnreanimated.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_rnreanimated

build rnreanimated_autolinked_build/react_codegen_rnreanimated: phony rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ComponentDescriptors.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/EventEmitters.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/Props.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/ShadowNodes.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/States.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/react/renderer/components/rnreanimated/rnreanimatedJSI-generated.cpp.o rnreanimated_autolinked_build/CMakeFiles/react_codegen_rnreanimated.dir/rnreanimated-generated.cpp.o


#############################################
# Utility command for edit_cache

build rnreanimated_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\rnreanimated_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnreanimated_autolinked_build/edit_cache: phony rnreanimated_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\rnreanimated_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Downloads\tms_application\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnreanimated_autolinked_build/rebuild_cache: phony rnreanimated_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Order-only phony target for react_codegen_safeareacontext

build cmake_object_order_depends_target_react_codegen_safeareacontext: phony || safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e9cdfdf598c36f9f5dde161c77e256f7/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\e9cdfdf598c36f9f5dde161c77e256f7\safeareacontext\RNCSafeAreaViewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\e9cdfdf598c36f9f5dde161c77e256f7\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e9cdfdf598c36f9f5dde161c77e256f7/safeareacontext/RNCSafeAreaViewState.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/common/cpp/react/renderer/components/safeareacontext/RNCSafeAreaViewState.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\e9cdfdf598c36f9f5dde161c77e256f7\safeareacontext\RNCSafeAreaViewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\e9cdfdf598c36f9f5dde161c77e256f7\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/d964f9605c408e7c74959de39ebacb99/safeareacontext/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\d964f9605c408e7c74959de39ebacb99\safeareacontext\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\d964f9605c408e7c74959de39ebacb99\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ac2523bb7a6072b55dfee4e3dcf2296d/components/safeareacontext/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ac2523bb7a6072b55dfee4e3dcf2296d\components\safeareacontext\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ac2523bb7a6072b55dfee4e3dcf2296d\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/014d9f477a27046bf718b8dc8a224348/renderer/components/safeareacontext/Props.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/Props.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\014d9f477a27046bf718b8dc8a224348\renderer\components\safeareacontext\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\014d9f477a27046bf718b8dc8a224348\renderer\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ac2523bb7a6072b55dfee4e3dcf2296d/components/safeareacontext/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ac2523bb7a6072b55dfee4e3dcf2296d\components\safeareacontext\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ac2523bb7a6072b55dfee4e3dcf2296d\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ac2523bb7a6072b55dfee4e3dcf2296d/components/safeareacontext/States.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/States.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ac2523bb7a6072b55dfee4e3dcf2296d\components\safeareacontext\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\ac2523bb7a6072b55dfee4e3dcf2296d\components\safeareacontext
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/db87085af6f1272be068bc6682b6b235/safeareacontextJSI-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/react/renderer/components/safeareacontext/safeareacontextJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\db87085af6f1272be068bc6682b6b235\safeareacontextJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\db87085af6f1272be068bc6682b6b235
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"

build safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c8abde0875859ca4fcaa0a2015e8162c/codegen/jni/safeareacontext-generated.cpp.o: CXX_COMPILER__react_codegen_safeareacontext_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/build/generated/source/codegen/jni/safeareacontext-generated.cpp || cmake_object_order_depends_target_react_codegen_safeareacontext
  DEFINES = -Dreact_codegen_safeareacontext_EXPORTS
  DEP_FILE = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\c8abde0875859ca4fcaa0a2015e8162c\codegen\jni\safeareacontext-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  OBJECT_FILE_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\c8abde0875859ca4fcaa0a2015e8162c\codegen\jni
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_safeareacontext


#############################################
# Link the shared library C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.so

build C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_safeareacontext.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_safeareacontext_Debug safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e9cdfdf598c36f9f5dde161c77e256f7/safeareacontext/RNCSafeAreaViewShadowNode.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/e9cdfdf598c36f9f5dde161c77e256f7/safeareacontext/RNCSafeAreaViewState.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/d964f9605c408e7c74959de39ebacb99/safeareacontext/ComponentDescriptors.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ac2523bb7a6072b55dfee4e3dcf2296d/components/safeareacontext/EventEmitters.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/014d9f477a27046bf718b8dc8a224348/renderer/components/safeareacontext/Props.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ac2523bb7a6072b55dfee4e3dcf2296d/components/safeareacontext/ShadowNodes.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/ac2523bb7a6072b55dfee4e3dcf2296d/components/safeareacontext/States.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/db87085af6f1272be068bc6682b6b235/safeareacontextJSI-generated.cpp.o safeareacontext_autolinked_build/CMakeFiles/react_codegen_safeareacontext.dir/c8abde0875859ca4fcaa0a2015e8162c/codegen/jni/safeareacontext-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"  -latomic -lm
  OBJECT_DIR = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_safeareacontext.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = safeareacontext_autolinked_build\CMakeFiles\react_codegen_safeareacontext.dir\
  TARGET_FILE = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.so"
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_safeareacontext.pdb"


#############################################
# Utility command for edit_cache

build safeareacontext_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\safeareacontext_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build safeareacontext_autolinked_build/edit_cache: phony safeareacontext_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\safeareacontext_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Downloads\tms_application\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build safeareacontext_autolinked_build/rebuild_cache: phony safeareacontext_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Order-only phony target for react_codegen_rnscreens

build cmake_object_order_depends_target_react_codegen_rnscreens: phony || rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6ead79318957c03ed8f906ee031915ce/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6ead79318957c03ed8f906ee031915ce\components\rnscreens\RNSFullWindowOverlayShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6ead79318957c03ed8f906ee031915ce\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6ead79318957c03ed8f906ee031915ce/components/rnscreens/RNSModalScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSModalScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6ead79318957c03ed8f906ee031915ce\components\rnscreens\RNSModalScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6ead79318957c03ed8f906ee031915ce\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f1288c5fca14d4afe46c25257650f490/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f1288c5fca14d4afe46c25257650f490\renderer\components\rnscreens\RNSScreenShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\f1288c5fca14d4afe46c25257650f490\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/600f8b3b161f1d05d1c9878528ae1e12/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\600f8b3b161f1d05d1c9878528ae1e12\rnscreens\RNSScreenStackHeaderConfigShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\600f8b3b161f1d05d1c9878528ae1e12\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6ead79318957c03ed8f906ee031915ce/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderConfigState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6ead79318957c03ed8f906ee031915ce\components\rnscreens\RNSScreenStackHeaderConfigState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6ead79318957c03ed8f906ee031915ce\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/600f8b3b161f1d05d1c9878528ae1e12/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\600f8b3b161f1d05d1c9878528ae1e12\rnscreens\RNSScreenStackHeaderSubviewShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\600f8b3b161f1d05d1c9878528ae1e12\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6ead79318957c03ed8f906ee031915ce/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6ead79318957c03ed8f906ee031915ce\components\rnscreens\RNSScreenStackHeaderSubviewState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\6ead79318957c03ed8f906ee031915ce\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/88da947241dad3dadf81509cd731d749/react/renderer/components/rnscreens/RNSScreenState.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/common/cpp/react/renderer/components/rnscreens/RNSScreenState.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\88da947241dad3dadf81509cd731d749\react\renderer\components\rnscreens\RNSScreenState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\88da947241dad3dadf81509cd731d749\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/rnscreens.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\rnscreens.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cb89e0380aecd13a0ebac197b0ace786/renderer/components/rnscreens/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\cb89e0380aecd13a0ebac197b0ace786\renderer\components\rnscreens\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\cb89e0380aecd13a0ebac197b0ace786\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0fc231fe0284decc369d4e3e0354a243/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0fc231fe0284decc369d4e3e0354a243\jni\react\renderer\components\rnscreens\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0fc231fe0284decc369d4e3e0354a243\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/49c1083f159e8e974430c81c5b5e7d19/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/Props.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\49c1083f159e8e974430c81c5b5e7d19\codegen\jni\react\renderer\components\rnscreens\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\49c1083f159e8e974430c81c5b5e7d19\codegen\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0fc231fe0284decc369d4e3e0354a243/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0fc231fe0284decc369d4e3e0354a243\jni\react\renderer\components\rnscreens\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0fc231fe0284decc369d4e3e0354a243\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0fc231fe0284decc369d4e3e0354a243/jni/react/renderer/components/rnscreens/States.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/States.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0fc231fe0284decc369d4e3e0354a243\jni\react\renderer\components\rnscreens\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\0fc231fe0284decc369d4e3e0354a243\jni\react\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"

build rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cb89e0380aecd13a0ebac197b0ace786/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnscreens_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/build/generated/source/codegen/jni/react/renderer/components/rnscreens/rnscreensJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnscreens
  DEFINES = -Dreact_codegen_rnscreens_EXPORTS
  DEP_FILE = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\cb89e0380aecd13a0ebac197b0ace786\renderer\components\rnscreens\rnscreensJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  OBJECT_FILE_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\cb89e0380aecd13a0ebac197b0ace786\renderer\components\rnscreens
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnscreens


#############################################
# Link the shared library C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.so

build C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnscreens.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnscreens_Debug rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6ead79318957c03ed8f906ee031915ce/components/rnscreens/RNSFullWindowOverlayShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6ead79318957c03ed8f906ee031915ce/components/rnscreens/RNSModalScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/f1288c5fca14d4afe46c25257650f490/renderer/components/rnscreens/RNSScreenShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/600f8b3b161f1d05d1c9878528ae1e12/rnscreens/RNSScreenStackHeaderConfigShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6ead79318957c03ed8f906ee031915ce/components/rnscreens/RNSScreenStackHeaderConfigState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/600f8b3b161f1d05d1c9878528ae1e12/rnscreens/RNSScreenStackHeaderSubviewShadowNode.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/6ead79318957c03ed8f906ee031915ce/components/rnscreens/RNSScreenStackHeaderSubviewState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/88da947241dad3dadf81509cd731d749/react/renderer/components/rnscreens/RNSScreenState.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/rnscreens.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cb89e0380aecd13a0ebac197b0ace786/renderer/components/rnscreens/ComponentDescriptors.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0fc231fe0284decc369d4e3e0354a243/jni/react/renderer/components/rnscreens/EventEmitters.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/49c1083f159e8e974430c81c5b5e7d19/codegen/jni/react/renderer/components/rnscreens/Props.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0fc231fe0284decc369d4e3e0354a243/jni/react/renderer/components/rnscreens/ShadowNodes.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/0fc231fe0284decc369d4e3e0354a243/jni/react/renderer/components/rnscreens/States.cpp.o rnscreens_autolinked_build/CMakeFiles/react_codegen_rnscreens.dir/cb89e0380aecd13a0ebac197b0ace786/renderer/components/rnscreens/rnscreensJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"  -latomic -lm
  OBJECT_DIR = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnscreens.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnscreens_autolinked_build\CMakeFiles\react_codegen_rnscreens.dir\
  TARGET_FILE = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.so"
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnscreens.pdb"


#############################################
# Utility command for edit_cache

build rnscreens_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\rnscreens_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnscreens_autolinked_build/edit_cache: phony rnscreens_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\rnscreens_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Downloads\tms_application\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnscreens_autolinked_build/rebuild_cache: phony rnscreens_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Order-only phony target for react_codegen_rnsvg

build cmake_object_order_depends_target_react_codegen_rnsvg: phony || rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/8ca7e2b0ea3063b18324eed675811eb6/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\8ca7e2b0ea3063b18324eed675811eb6\cpp\react\renderer\components\rnsvg\RNSVGImageShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\8ca7e2b0ea3063b18324eed675811eb6\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/eb26571bd426259caaf4abfcfbffd495/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\eb26571bd426259caaf4abfcfbffd495\common\cpp\react\renderer\components\rnsvg\RNSVGImageState.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\eb26571bd426259caaf4abfcfbffd495\common\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/8ca7e2b0ea3063b18324eed675811eb6/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\8ca7e2b0ea3063b18324eed675811eb6\cpp\react\renderer\components\rnsvg\RNSVGLayoutableShadowNode.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\8ca7e2b0ea3063b18324eed675811eb6\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/eb26571bd426259caaf4abfcfbffd495/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\eb26571bd426259caaf4abfcfbffd495\common\cpp\react\renderer\components\rnsvg\RNSVGShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\eb26571bd426259caaf4abfcfbffd495\common\cpp\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/rnsvg.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\rnsvg.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/202251dbe73206f5ff29b638dd851148/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\202251dbe73206f5ff29b638dd851148\jni\react\renderer\components\rnsvg\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\202251dbe73206f5ff29b638dd851148\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/3f24ff70fc6bbd99ff7a3cf11aff461f/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\3f24ff70fc6bbd99ff7a3cf11aff461f\codegen\jni\react\renderer\components\rnsvg\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\3f24ff70fc6bbd99ff7a3cf11aff461f\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/9bade7e06bca23e77d9cd6307885b1ec/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\9bade7e06bca23e77d9cd6307885b1ec\source\codegen\jni\react\renderer\components\rnsvg\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\9bade7e06bca23e77d9cd6307885b1ec\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/3f24ff70fc6bbd99ff7a3cf11aff461f/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\3f24ff70fc6bbd99ff7a3cf11aff461f\codegen\jni\react\renderer\components\rnsvg\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\3f24ff70fc6bbd99ff7a3cf11aff461f\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/9bade7e06bca23e77d9cd6307885b1ec/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/States.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\9bade7e06bca23e77d9cd6307885b1ec\source\codegen\jni\react\renderer\components\rnsvg\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\9bade7e06bca23e77d9cd6307885b1ec\source\codegen\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"

build rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/202251dbe73206f5ff29b638dd851148/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o: CXX_COMPILER__react_codegen_rnsvg_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/build/generated/source/codegen/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_rnsvg
  DEFINES = -Dreact_codegen_rnsvg_EXPORTS
  DEP_FILE = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\202251dbe73206f5ff29b638dd851148\jni\react\renderer\components\rnsvg\rnsvgJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -DLOG_TAG=\"ReactNative\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../../common/cpp" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni" -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnsvg" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include"
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  OBJECT_FILE_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\202251dbe73206f5ff29b638dd851148\jni\react\renderer\components\rnsvg
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"


# =============================================================================
# Link build statements for SHARED_LIBRARY target react_codegen_rnsvg


#############################################
# Link the shared library C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.so

build C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnsvg.so: CXX_SHARED_LIBRARY_LINKER__react_codegen_rnsvg_Debug rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/8ca7e2b0ea3063b18324eed675811eb6/cpp/react/renderer/components/rnsvg/RNSVGImageShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/eb26571bd426259caaf4abfcfbffd495/common/cpp/react/renderer/components/rnsvg/RNSVGImageState.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/8ca7e2b0ea3063b18324eed675811eb6/cpp/react/renderer/components/rnsvg/RNSVGLayoutableShadowNode.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/eb26571bd426259caaf4abfcfbffd495/common/cpp/react/renderer/components/rnsvg/RNSVGShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/rnsvg.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/202251dbe73206f5ff29b638dd851148/jni/react/renderer/components/rnsvg/ComponentDescriptors.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/3f24ff70fc6bbd99ff7a3cf11aff461f/codegen/jni/react/renderer/components/rnsvg/EventEmitters.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/9bade7e06bca23e77d9cd6307885b1ec/source/codegen/jni/react/renderer/components/rnsvg/Props.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/3f24ff70fc6bbd99ff7a3cf11aff461f/codegen/jni/react/renderer/components/rnsvg/ShadowNodes.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/9bade7e06bca23e77d9cd6307885b1ec/source/codegen/jni/react/renderer/components/rnsvg/States.cpp.o rnsvg_autolinked_build/CMakeFiles/react_codegen_rnsvg.dir/202251dbe73206f5ff29b638dd851148/jni/react/renderer/components/rnsvg/rnsvgJSI-generated.cpp.o | C$:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so C$:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so
  LANGUAGE_COMPILE_FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -DREACT_NATIVE_MINOR_VERSION=79 -fno-limit-debug-info
  LINK_FLAGS = -Wl,-z,max-page-size=16384 -Wl,--build-id=sha1 -Wl,--no-rosegment -Wl,--no-undefined-version -Wl,--fatal-warnings -Wl,--no-undefined -Qunused-arguments
  LINK_LIBRARIES = "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.arm64-v8a/libreactnative.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.arm64-v8a/libjsi.so"  "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.arm64-v8a/libfbjni.so"  -latomic -lm
  OBJECT_DIR = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  SONAME = libreact_codegen_rnsvg.so
  SONAME_FLAG = -Wl,-soname,
  TARGET_COMPILE_PDB = rnsvg_autolinked_build\CMakeFiles\react_codegen_rnsvg.dir\
  TARGET_FILE = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.so"
  TARGET_PDB = "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libreact_codegen_rnsvg.pdb"


#############################################
# Utility command for edit_cache

build rnsvg_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\rnsvg_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build rnsvg_autolinked_build/edit_cache: phony rnsvg_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\rnsvg_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Downloads\tms_application\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rnsvg_autolinked_build/rebuild_cache: phony rnsvg_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNCWebViewSpec


#############################################
# Order-only phony target for react_codegen_RNCWebViewSpec

build cmake_object_order_depends_target_react_codegen_RNCWebViewSpec: phony || RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/RNCWebViewSpec-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\RNCWebViewSpec-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/Props.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\RNCWebViewSpecJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""

build RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o: CXX_COMPILER__react_codegen_RNCWebViewSpec_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec/States.cpp || cmake_object_order_depends_target_react_codegen_RNCWebViewSpec
  DEP_FILE = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir
  OBJECT_FILE_DIR = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\react\renderer\components\RNCWebViewSpec
  TARGET_COMPILE_PDB = RNCWebViewSpec_autolinked_build\CMakeFiles\react_codegen_RNCWebViewSpec.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNCWebViewSpec

build RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/RNCWebViewSpec-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ComponentDescriptors.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/EventEmitters.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/Props.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/RNCWebViewSpecJSI-generated.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/ShadowNodes.cpp.o RNCWebViewSpec_autolinked_build/CMakeFiles/react_codegen_RNCWebViewSpec.dir/react/renderer/components/RNCWebViewSpec/States.cpp.o


#############################################
# Utility command for edit_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\RNCWebViewSpec_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNCWebViewSpec_autolinked_build/edit_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\RNCWebViewSpec_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Downloads\tms_application\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNCWebViewSpec_autolinked_build/rebuild_cache: phony RNCWebViewSpec_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake
# =============================================================================

# =============================================================================
# Object build statements for OBJECT_LIBRARY target react_codegen_RNEdgeToEdge


#############################################
# Order-only phony target for react_codegen_RNEdgeToEdge

build cmake_object_order_depends_target_react_codegen_RNEdgeToEdge: phony || RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/RNEdgeToEdge-generated.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\RNEdgeToEdge-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\ComponentDescriptors.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\EventEmitters.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/Props.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\Props.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\RNEdgeToEdgeJSI-generated.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\ShadowNodes.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\
  TARGET_PDB = ""

build RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o: CXX_COMPILER__react_codegen_RNEdgeToEdge_Debug C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge/States.cpp || cmake_object_order_depends_target_react_codegen_RNEdgeToEdge
  DEP_FILE = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge\States.cpp.o.d
  FLAGS = -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\"ReactNative\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1
  INCLUDES = -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/." -I"C:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/aae2e161ad8460d34e386c90489cc2b4/transformed/fbjni-0.7.0/prefab/modules/fbjni/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include" -isystem "C:/Users/<USER>/.gradle/caches/8.13/transforms/5a38de6e1ef4b6972bc76438000ddaf4/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include"
  OBJECT_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir
  OBJECT_FILE_DIR = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\react\renderer\components\RNEdgeToEdge
  TARGET_COMPILE_PDB = RNEdgeToEdge_autolinked_build\CMakeFiles\react_codegen_RNEdgeToEdge.dir\
  TARGET_PDB = ""



#############################################
# Object library react_codegen_RNEdgeToEdge

build RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge: phony RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/RNEdgeToEdge-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ComponentDescriptors.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/EventEmitters.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/Props.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/RNEdgeToEdgeJSI-generated.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/ShadowNodes.cpp.o RNEdgeToEdge_autolinked_build/CMakeFiles/react_codegen_RNEdgeToEdge.dir/react/renderer/components/RNEdgeToEdge/States.cpp.o


#############################################
# Utility command for edit_cache

build RNEdgeToEdge_autolinked_build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\RNEdgeToEdge_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build RNEdgeToEdge_autolinked_build/edit_cache: phony RNEdgeToEdge_autolinked_build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build RNEdgeToEdge_autolinked_build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D "C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a\RNEdgeToEdge_autolinked_build" && D:\AndroidSdk\cmake\3.22.1\bin\cmake.exe --regenerate-during-build -S"C:\Users\<USER>\Downloads\tms_application\node_modules\react-native\ReactAndroid\cmake-utils\default-app-setup" -B"C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a""
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build RNEdgeToEdge_autolinked_build/rebuild_cache: phony RNEdgeToEdge_autolinked_build/CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build appmodules: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libappmodules.so

build libappmodules.so: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libappmodules.so

build libreact_codegen_rnscreens.so: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnscreens.so

build libreact_codegen_rnsvg.so: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnsvg.so

build libreact_codegen_safeareacontext.so: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_safeareacontext.so

build react_codegen_RNCWebViewSpec: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

build react_codegen_RNDateTimePickerCGen: phony RNDateTimePickerCGen_autolinked_build/react_codegen_RNDateTimePickerCGen

build react_codegen_RNEdgeToEdge: phony RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge

build react_codegen_rnasyncstorage: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

build react_codegen_rngesturehandler_codegen: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

build react_codegen_rnreanimated: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

build react_codegen_rnscreens: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnscreens.so

build react_codegen_rnsvg: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnsvg.so

build react_codegen_safeareacontext: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a

build all: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libappmodules.so rnasyncstorage_autolinked_build/all RNDateTimePickerCGen_autolinked_build/all rngesturehandler_codegen_autolinked_build/all rnreanimated_autolinked_build/all safeareacontext_autolinked_build/all rnscreens_autolinked_build/all rnsvg_autolinked_build/all RNCWebViewSpec_autolinked_build/all RNEdgeToEdge_autolinked_build/all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/RNCWebViewSpec_autolinked_build

build RNCWebViewSpec_autolinked_build/all: phony RNCWebViewSpec_autolinked_build/react_codegen_RNCWebViewSpec

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/RNDateTimePickerCGen_autolinked_build

build RNDateTimePickerCGen_autolinked_build/all: phony RNDateTimePickerCGen_autolinked_build/react_codegen_RNDateTimePickerCGen

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/RNEdgeToEdge_autolinked_build

build RNEdgeToEdge_autolinked_build/all: phony RNEdgeToEdge_autolinked_build/react_codegen_RNEdgeToEdge

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/rnasyncstorage_autolinked_build

build rnasyncstorage_autolinked_build/all: phony rnasyncstorage_autolinked_build/react_codegen_rnasyncstorage

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/rngesturehandler_codegen_autolinked_build

build rngesturehandler_codegen_autolinked_build/all: phony rngesturehandler_codegen_autolinked_build/react_codegen_rngesturehandler_codegen

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/rnreanimated_autolinked_build

build rnreanimated_autolinked_build/all: phony rnreanimated_autolinked_build/react_codegen_rnreanimated

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/rnscreens_autolinked_build

build rnscreens_autolinked_build/all: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnscreens.so

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/rnsvg_autolinked_build

build rnsvg_autolinked_build/all: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_rnsvg.so

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/safeareacontext_autolinked_build

build safeareacontext_autolinked_build/all: phony C$:/Users/<USER>/Downloads/tms_application/android/app/build/intermediates/cxx/Debug/6u4m1n71/obj/arm64-v8a/libreact_codegen_safeareacontext.so

# =============================================================================
# Built-in targets


#############################################
# Phony target to force glob verification run.

build C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force: phony


#############################################
# Re-run CMake to check if globbed directories changed.

build C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/CMakeFiles/cmake.verify_globs: VERIFY_GLOBS | C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/CMakeFiles/VerifyGlobs.cmake_force
  pool = console
  restat = 1


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/CMakeFiles/cmake.verify_globs | C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/abis.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/flags.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/platforms.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/arm64-v8a/CMakeFiles/VerifyGlobs.cmake C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfig.cmake C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/ReactAndroid/ReactAndroidConfigVersion.cmake C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfig.cmake C$:/Users/<USER>/Downloads/tms_application/android/app/.cxx/Debug/6u4m1n71/prefab/arm64-v8a/prefab/lib/aarch64-linux-android/cmake/fbjni/fbjniConfigVersion.cmake C$:/Users/<USER>/Downloads/tms_application/android/app/build/generated/autolinking/src/main/jni/Android-autolinking.cmake C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/@react-native-community/datetimepicker/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-svg/android/src/main/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native/ReactAndroid/cmake-utils/ReactNative-application.cmake C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt C$:/Users/<USER>/Downloads/tms_application/node_modules/react-native/ReactAndroid/cmake-utils/folly-flags.cmake CMakeCache.txt CMakeFiles/3.22.1-g37088a8-dirty/CMakeCCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeCXXCompiler.cmake CMakeFiles/3.22.1-g37088a8-dirty/CMakeSystem.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompiler.cmake.in D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCCompilerABI.c D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCInformation.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompiler.cmake.in D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXCompilerABI.cpp D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCXXInformation.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCommonLanguageInclude.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeCompilerIdDetection.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCXXCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompileFeatures.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerABI.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineCompilerId.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeDetermineSystem.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeFindBinUtils.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeGenericSystem.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeInitializeConfigs.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeLanguageInformation.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitIncludeInfo.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseImplicitLinkInfo.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeParseLibraryArchitecture.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystem.cmake.in D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInformation.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeSystemSpecificInitialize.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCXXCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/CMakeTestCompilerCommon.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ADSP-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMCC-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/ARMClang-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/AppleClang-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Borland-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Bruce-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/CMakeCommonCompilerMacros.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-C.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-CXX.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-DetermineCompilerInternal.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang-FindBinUtils.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Clang.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Comeau-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Cray-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Embarcadero-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Fujitsu-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GHS-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/GNU.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/HP-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IAR-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Intel-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/MSVC-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVHPC-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/NVIDIA-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PGI-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/PathScale-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SCO-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SDCC-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TI-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/Watcom-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XL-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-C-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Internal/FeatureTesting.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-C.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang-CXX.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Clang.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-C.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine-CXX.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Determine.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android-Initialize.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Android/Determine-Compiler.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/Linux.cmake D$:/AndroidSdk/cmake/3.22.1/share/cmake-3.22/Modules/Platform/UnixPaths.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/abis.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/android-legacy.toolchain.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/flags.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Clang.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Determine.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android-Initialize.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/hooks/pre/Android.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/hooks/pre/Determine-Compiler.cmake D$:/AndroidSdk/ndk/27.1.12297006/build/cmake/platforms.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
