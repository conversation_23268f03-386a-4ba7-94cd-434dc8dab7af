ninja: Entering directory `C:\Users\<USER>\Downloads\tms_application\android\app\.cxx\Debug\6u4m1n71\arm64-v8a'
[0/2] Re-checking globbed directories...
[1/3] Building CXX object rngesturehandler_codegen_autolinked_build/CMakeFiles/react_codegen_rngesturehandler_codegen.dir/react/renderer/components/rngesturehandler_codegen/rngesturehandler_codegenJSI-generated.cpp.o
[2/3] Building CXX object RNDateTimePickerCGen_autolinked_build/CMakeFiles/react_codegen_RNDateTimePickerCGen.dir/react/renderer/components/RNDateTimePickerCGen/RNDateTimePickerCGenJSI-generated.cpp.o
[3/3] Linking CXX shared library "C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\cxx\Debug\6u4m1n71\obj\arm64-v8a\libappmodules.so"
