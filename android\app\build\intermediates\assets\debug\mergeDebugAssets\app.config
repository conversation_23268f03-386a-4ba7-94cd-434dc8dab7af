{"jsEngine": "hermes", "scheme": "tmsapp", "name": "TmsApp", "slug": "tms_app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "userInterfaceStyle": "light", "assetBundlePatterns": ["assets/**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.emrald.tms", "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "UIStatusBarStyle": "UIStatusBarStyleLightContent", "UIViewControllerBasedStatusBarAppearance": false}}, "android": {"jsEngine": "hermes", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.ramsha_malik.tms_app"}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff", "imageWidth": 150}]], "extra": {"router": {}, "eas": {"projectId": "aec7ddf4-9474-4131-9a9a-4b9309bf4176"}}, "runtimeVersion": "1.0.0", "updates": {"url": "https://u.expo.dev/aec7ddf4-9474-4131-9a9a-4b9309bf4176"}, "sdkVersion": "53.0.0", "platforms": ["ios", "android"], "androidStatusBar": {"backgroundColor": "#ffffff"}}