{"logs": [{"outputFile": "com.ramsha_malik.tms_app-mergeDebugResources-57:/values-gu/values-gu.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\428bacad40a6f9fbc888386871dcda9e\\transformed\\material-1.12.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,342,414,496,602,700,799,919,1003,1060,1123,1214,1281,1340,1430,1493,1558,1622,1691,1753,1807,1922,1980,2041,2095,2168,2295,2381,2463,2562,2647,2731,2864,2939,3015,3148,3234,3315,3369,3421,3487,3560,3640,3711,3791,3862,3938,4017,4086,4193,4289,4367,4462,4558,4632,4707,4806,4857,4939,5006,5093,5183,5245,5309,5372,5439,5541,5646,5743,5845,5903,5959,6037,6123,6198", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,73,71,81,105,97,98,119,83,56,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77,85,74,72", "endOffsets": "263,337,409,491,597,695,794,914,998,1055,1118,1209,1276,1335,1425,1488,1553,1617,1686,1748,1802,1917,1975,2036,2090,2163,2290,2376,2458,2557,2642,2726,2859,2934,3010,3143,3229,3310,3364,3416,3482,3555,3635,3706,3786,3857,3933,4012,4081,4188,4284,4362,4457,4553,4627,4702,4801,4852,4934,5001,5088,5178,5240,5304,5367,5434,5536,5641,5738,5840,5898,5954,6032,6118,6193,6266"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,50,51,52,54,57,59,60,61,62,63,64,65,66,67,68,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,126,127,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3070,3144,3216,3298,3404,4220,4319,4439,4602,4659,4722,4884,5098,5224,5314,5377,5442,5506,5575,5637,5691,5806,5864,5925,5979,6052,6396,6482,6564,6663,6748,6832,6965,7040,7116,7249,7335,7416,7470,7522,7588,7661,7741,7812,7892,7963,8039,8118,8187,8294,8390,8468,8563,8659,8733,8808,8907,8958,9040,9107,9194,9284,9346,9410,9473,9540,9642,9747,9844,9946,10004,10060,10527,10613,10688", "endLines": "5,34,35,36,37,38,46,47,48,50,51,52,54,57,59,60,61,62,63,64,65,66,67,68,69,70,71,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,126,127,128", "endColumns": "12,73,71,81,105,97,98,119,83,56,62,90,66,58,89,62,64,63,68,61,53,114,57,60,53,72,126,85,81,98,84,83,132,74,75,132,85,80,53,51,65,72,79,70,79,70,75,78,68,106,95,77,94,95,73,74,98,50,81,66,86,89,61,63,62,66,101,104,96,101,57,55,77,85,74,72", "endOffsets": "313,3139,3211,3293,3399,3497,4314,4434,4518,4654,4717,4808,4946,5152,5309,5372,5437,5501,5570,5632,5686,5801,5859,5920,5974,6047,6174,6477,6559,6658,6743,6827,6960,7035,7111,7244,7330,7411,7465,7517,7583,7656,7736,7807,7887,7958,8034,8113,8182,8289,8385,8463,8558,8654,8728,8803,8902,8953,9035,9102,9189,9279,9341,9405,9468,9535,9637,9742,9839,9941,9999,10055,10133,10608,10683,10756"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a38de6e1ef4b6972bc76438000ddaf4\\transformed\\react-android-0.79.3-debug\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,124,203,274,342,421,488,555,629,705,785,866,934,1013,1091,1166,1245,1325,1405,1476,1547,1646,1718,1793,1862", "endColumns": "68,78,70,67,78,66,66,73,75,79,80,67,78,77,74,78,79,79,70,70,98,71,74,68,72", "endOffsets": "119,198,269,337,416,483,550,624,700,780,861,929,1008,1086,1161,1240,1320,1400,1471,1542,1641,1713,1788,1857,1930"}, "to": {"startLines": "33,49,53,55,56,58,72,73,74,121,122,123,124,129,130,131,132,133,134,135,136,138,139,140,141", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3001,4523,4813,4951,5019,5157,6179,6246,6320,10138,10218,10299,10367,10761,10839,10914,10993,11073,11153,11224,11295,11495,11567,11642,11711", "endColumns": "68,78,70,67,78,66,66,73,75,79,80,67,78,77,74,78,79,79,70,70,98,71,74,68,72", "endOffsets": "3065,4597,4879,5014,5093,5219,6241,6315,6391,10213,10294,10362,10441,10834,10909,10988,11068,11148,11219,11290,11389,11562,11637,11706,11779"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bce72c8d6c2b594d45298a87e02b9e4\\transformed\\appcompat-1.7.0\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,316,423,510,610,730,808,885,976,1069,1164,1258,1358,1451,1546,1640,1731,1822,1902,2008,2109,2206,2315,2415,2525,2685,2788", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "207,311,418,505,605,725,803,880,971,1064,1159,1253,1353,1446,1541,1635,1726,1817,1897,2003,2104,2201,2310,2410,2520,2680,2783,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,125", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,425,529,636,723,823,943,1021,1098,1189,1282,1377,1471,1571,1664,1759,1853,1944,2035,2115,2221,2322,2419,2528,2628,2738,2898,10446", "endColumns": "106,103,106,86,99,119,77,76,90,92,94,93,99,92,94,93,90,90,79,105,100,96,108,99,109,159,102,80", "endOffsets": "420,524,631,718,818,938,1016,1093,1184,1277,1372,1466,1566,1659,1754,1848,1939,2030,2110,2216,2317,2414,2523,2623,2733,2893,2996,10522"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dbf0f811c0b4cd74b2ceefe96db4396e\\transformed\\core-1.13.1\\res\\values-gu\\values-gu.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,252,349,451,553,651,773", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "144,247,344,446,548,646,768,869"}, "to": {"startLines": "39,40,41,42,43,44,45,137", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3502,3596,3699,3796,3898,4000,4098,11394", "endColumns": "93,102,96,101,101,97,121,100", "endOffsets": "3591,3694,3791,3893,3995,4093,4215,11490"}}]}]}