{"logs": [{"outputFile": "com.ramsha_malik.tms_app-mergeDebugResources-57:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a38de6e1ef4b6972bc76438000ddaf4\\transformed\\react-android-0.79.3-debug\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,126,201,274,342,422,490,565,643,725,813,887,966,1047,1124,1207,1290,1368,1442,1513,1596,1671,1755,1825", "endColumns": "70,74,72,67,79,67,74,77,81,87,73,78,80,76,82,82,77,73,70,82,74,83,69,78", "endOffsets": "121,196,269,337,417,485,560,638,720,808,882,961,1042,1119,1202,1285,1363,1437,1508,1591,1666,1750,1820,1899"}, "to": {"startLines": "33,49,53,55,56,71,72,73,120,121,122,123,128,129,130,131,132,133,134,135,137,138,139,140", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3038,4559,4843,4984,5052,6160,6228,6303,10096,10178,10266,10340,10746,10827,10904,10987,11070,11148,11222,11293,11477,11552,11636,11706", "endColumns": "70,74,72,67,79,67,74,77,81,87,73,78,80,76,82,82,77,73,70,82,74,83,69,78", "endOffsets": "3104,4629,4911,5047,5127,6223,6298,6376,10173,10261,10335,10414,10822,10899,10982,11065,11143,11217,11288,11371,11547,11631,11701,11780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bce72c8d6c2b594d45298a87e02b9e4\\transformed\\appcompat-1.7.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,310,421,507,609,726,807,884,976,1070,1166,1268,1377,1471,1572,1666,1758,1851,1934,2045,2149,2248,2358,2460,2559,2725,2827", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "206,305,416,502,604,721,802,879,971,1065,1161,1263,1372,1466,1567,1661,1753,1846,1929,2040,2144,2243,2353,2455,2554,2720,2822,2905"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,124", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "316,422,521,632,718,820,937,1018,1095,1187,1281,1377,1479,1588,1682,1783,1877,1969,2062,2145,2256,2360,2459,2569,2671,2770,2936,10419", "endColumns": "105,98,110,85,101,116,80,76,91,93,95,101,108,93,100,93,91,92,82,110,103,98,109,101,98,165,101,82", "endOffsets": "417,516,627,713,815,932,1013,1090,1182,1276,1372,1474,1583,1677,1778,1872,1964,2057,2140,2251,2355,2454,2564,2666,2765,2931,3033,10497"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dbf0f811c0b4cd74b2ceefe96db4396e\\transformed\\core-1.13.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "39,40,41,42,43,44,45,136", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3532,3627,3729,3827,3930,4036,4141,11376", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "3622,3724,3822,3925,4031,4136,4256,11472"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\428bacad40a6f9fbc888386871dcda9e\\transformed\\material-1.12.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,266,346,425,510,602,689,788,905,987,1047,1111,1196,1264,1328,1415,1479,1543,1602,1674,1738,1792,1911,1971,2032,2086,2159,2292,2376,2453,2546,2626,2719,2857,2937,3016,3142,3230,3309,3364,3415,3481,3554,3633,3704,3783,3856,3931,4005,4077,4190,4278,4355,4446,4538,4612,4686,4777,4831,4913,4982,5065,5151,5213,5277,5340,5408,5511,5614,5711,5812,5871,5926,6007,6096,6173", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "261,341,420,505,597,684,783,900,982,1042,1106,1191,1259,1323,1410,1474,1538,1597,1669,1733,1787,1906,1966,2027,2081,2154,2287,2371,2448,2541,2621,2714,2852,2932,3011,3137,3225,3304,3359,3410,3476,3549,3628,3699,3778,3851,3926,4000,4072,4185,4273,4350,4441,4533,4607,4681,4772,4826,4908,4977,5060,5146,5208,5272,5335,5403,5506,5609,5706,5807,5866,5921,6002,6091,6168,6246"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,50,51,52,54,57,58,59,60,61,62,63,64,65,66,67,68,69,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,125,126,127", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3109,3189,3268,3353,3445,4261,4360,4477,4634,4694,4758,4916,5132,5196,5283,5347,5411,5470,5542,5606,5660,5779,5839,5900,5954,6027,6381,6465,6542,6635,6715,6808,6946,7026,7105,7231,7319,7398,7453,7504,7570,7643,7722,7793,7872,7945,8020,8094,8166,8279,8367,8444,8535,8627,8701,8775,8866,8920,9002,9071,9154,9240,9302,9366,9429,9497,9600,9703,9800,9901,9960,10015,10502,10591,10668", "endLines": "5,34,35,36,37,38,46,47,48,50,51,52,54,57,58,59,60,61,62,63,64,65,66,67,68,69,70,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,125,126,127", "endColumns": "12,79,78,84,91,86,98,116,81,59,63,84,67,63,86,63,63,58,71,63,53,118,59,60,53,72,132,83,76,92,79,92,137,79,78,125,87,78,54,50,65,72,78,70,78,72,74,73,71,112,87,76,90,91,73,73,90,53,81,68,82,85,61,63,62,67,102,102,96,100,58,54,80,88,76,77", "endOffsets": "311,3184,3263,3348,3440,3527,4355,4472,4554,4689,4753,4838,4979,5191,5278,5342,5406,5465,5537,5601,5655,5774,5834,5895,5949,6022,6155,6460,6537,6630,6710,6803,6941,7021,7100,7226,7314,7393,7448,7499,7565,7638,7717,7788,7867,7940,8015,8089,8161,8274,8362,8439,8530,8622,8696,8770,8861,8915,8997,9066,9149,9235,9297,9361,9424,9492,9595,9698,9795,9896,9955,10010,10091,10586,10663,10741"}}]}]}