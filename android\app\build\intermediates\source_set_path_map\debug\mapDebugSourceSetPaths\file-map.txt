com.ramsha_malik.tms_app-recyclerview-1.1.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\1476c0e30f0769ac6fdc85451c0e5d30\transformed\recyclerview-1.1.0\res
com.ramsha_malik.tms_app-profileinstaller-1.3.1-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\17b992a4b70c45d4f3343d8faf0b3eb8\transformed\profileinstaller-1.3.1\res
com.ramsha_malik.tms_app-sqlite-framework-2.4.0-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\185ce690c716ca9fa4934371083c8ad5\transformed\sqlite-framework-2.4.0\res
com.ramsha_malik.tms_app-fragment-ktx-1.6.1-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\1ba0b3f8baddd5e3d3e96eee05383d41\transformed\fragment-ktx-1.6.1\res
com.ramsha_malik.tms_app-tracing-1.2.0-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\1d236445d4628779bcb002ed47832753\transformed\tracing-1.2.0\res
com.ramsha_malik.tms_app-startup-runtime-1.1.1-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\1d590517ffbbc1342bd47e61c5be20d2\transformed\startup-runtime-1.1.1\res
com.ramsha_malik.tms_app-lifecycle-runtime-ktx-2.6.2-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\25ff82f66f00c0a448d42fdfc3177c2b\transformed\lifecycle-runtime-ktx-2.6.2\res
com.ramsha_malik.tms_app-gif-3.0.3-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\263a9a41c0767b45cea635ab9a77cd78\transformed\gif-3.0.3\res
com.ramsha_malik.tms_app-lifecycle-viewmodel-savedstate-2.6.2-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\3767d29164fb986bd46212528a6afad6\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
com.ramsha_malik.tms_app-activity-1.8.0-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\3e5f58c5bcccbfa439b66f27bd15e544\transformed\activity-1.8.0\res
com.ramsha_malik.tms_app-glide-plugin-3.0.3-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\3f4b946ca4709998682f5290feede2c6\transformed\glide-plugin-3.0.3\res
com.ramsha_malik.tms_app-material-1.12.0-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\428bacad40a6f9fbc888386871dcda9e\transformed\material-1.12.0\res
com.ramsha_malik.tms_app-savedstate-ktx-1.2.1-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\44bc66389f11cbc3f4f1f10102d22906\transformed\savedstate-ktx-1.2.1\res
com.ramsha_malik.tms_app-apng-3.0.3-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\4d87c8a515546943f359f1c909351ec8\transformed\apng-3.0.3\res
com.ramsha_malik.tms_app-room-ktx-2.6.1-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\538f56bd6702d7f851974ade07f1d3d0\transformed\room-ktx-2.6.1\res
com.ramsha_malik.tms_app-emoji2-views-helper-1.3.0-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\55047732022e8794aa5d3acb9690614f\transformed\emoji2-views-helper-1.3.0\res
com.ramsha_malik.tms_app-react-android-0.79.3-debug-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\5a38de6e1ef4b6972bc76438000ddaf4\transformed\react-android-0.79.3-debug\res
com.ramsha_malik.tms_app-emoji2-1.3.0-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\5ac75e494d3e1d2a6be010fcc227aecf\transformed\emoji2-1.3.0\res
com.ramsha_malik.tms_app-lifecycle-process-2.6.2-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\5bb24a51c67a94a5cd269f41e82e078d\transformed\lifecycle-process-2.6.2\res
com.ramsha_malik.tms_app-media-1.0.0-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\5c275e354cf226cf1d26b8d4a9e0a6b5\transformed\media-1.0.0\res
com.ramsha_malik.tms_app-core-runtime-2.2.0-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\5f331e97afc05724e0d2c93117b55dd3\transformed\core-runtime-2.2.0\res
com.ramsha_malik.tms_app-constraintlayout-2.0.1-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\6bbf8a269e5a7d0b0f9afce42f29f59e\transformed\constraintlayout-2.0.1\res
com.ramsha_malik.tms_app-expo.modules.splashscreen-0.30.9-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\6cb4d9e172e1d7128fdcb3081e6424e0\transformed\expo.modules.splashscreen-0.30.9\res
com.ramsha_malik.tms_app-avif-3.0.3-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\6d651171d8687570f4efa73451faf338\transformed\avif-3.0.3\res
com.ramsha_malik.tms_app-core-ktx-1.13.1-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\6d96be588365241022bddada7f275a0b\transformed\core-ktx-1.13.1\res
com.ramsha_malik.tms_app-frameanimation-3.0.3-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\73c48d20dedefa9986b4d7194881229d\transformed\frameanimation-3.0.3\res
com.ramsha_malik.tms_app-autofill-1.1.0-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\74c11b1adb57be73089429ecf3a62add\transformed\autofill-1.1.0\res
com.ramsha_malik.tms_app-activity-ktx-1.8.0-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\75504bd3640385a5ec16738c32ca2058\transformed\activity-ktx-1.8.0\res
com.ramsha_malik.tms_app-glide-4.16.0-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\774615a759e0ba61d61c69d51912e48a\transformed\glide-4.16.0\res
com.ramsha_malik.tms_app-appcompat-1.7.0-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\7bce72c8d6c2b594d45298a87e02b9e4\transformed\appcompat-1.7.0\res
com.ramsha_malik.tms_app-appcompat-resources-1.7.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\7fc8b596b255ae8c98ca5573e02e2411\transformed\appcompat-resources-1.7.0\res
com.ramsha_malik.tms_app-lifecycle-livedata-core-2.6.2-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\83fb30c725d782917068d185e19d63b2\transformed\lifecycle-livedata-core-2.6.2\res
com.ramsha_malik.tms_app-swiperefreshlayout-1.1.0-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\8a227b17fcd6955f43f45d4812ce8033\transformed\swiperefreshlayout-1.1.0\res
com.ramsha_malik.tms_app-tracing-ktx-1.2.0-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\9977a7d2878a7cf4e601806aa78e5d60\transformed\tracing-ktx-1.2.0\res
com.ramsha_malik.tms_app-lifecycle-viewmodel-ktx-2.6.2-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\99b467659dbf138fd89efc85b6252007\transformed\lifecycle-viewmodel-ktx-2.6.2\res
com.ramsha_malik.tms_app-expo.modules.sharing-13.1.5-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\99eeaa698886199b824f04db24c6bff1\transformed\expo.modules.sharing-13.1.5\res
com.ramsha_malik.tms_app-savedstate-1.2.1-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\a21e9970dce3defffb3381a209207c49\transformed\savedstate-1.2.1\res
com.ramsha_malik.tms_app-awebp-3.0.3-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\ace0de5ee8c19ba389bcafcdcd6c6ec1\transformed\awebp-3.0.3\res
com.ramsha_malik.tms_app-core-splashscreen-1.2.0-alpha02-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\afbbcd6a450aa139608eb98a5c17e4b5\transformed\core-splashscreen-1.2.0-alpha02\res
com.ramsha_malik.tms_app-lifecycle-livedata-core-ktx-2.6.2-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\befd3b786c1753c5da35d249fb278bfb\transformed\lifecycle-livedata-core-ktx-2.6.2\res
com.ramsha_malik.tms_app-lifecycle-livedata-2.6.2-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\c769dd71d0a4010028e8d58f776bd7df\transformed\lifecycle-livedata-2.6.2\res
com.ramsha_malik.tms_app-fragment-1.6.1-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\c7e0705fb1a1a2da2711e69b4fa8c45e\transformed\fragment-1.6.1\res
com.ramsha_malik.tms_app-lifecycle-viewmodel-2.6.2-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\caa1aace09bd06445050846a2d8d9033\transformed\lifecycle-viewmodel-2.6.2\res
com.ramsha_malik.tms_app-coordinatorlayout-1.2.0-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\cc6268b713348b2c2befd90d45d5efab\transformed\coordinatorlayout-1.2.0\res
com.ramsha_malik.tms_app-drawee-3.6.0-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\ce0b6a21885d314ccad7bed0d1a3a8d6\transformed\drawee-3.6.0\res
com.ramsha_malik.tms_app-drawerlayout-1.1.1-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\d4abc979e46c3a1c347b20026115e8c3\transformed\drawerlayout-1.1.1\res
com.ramsha_malik.tms_app-room-runtime-2.6.1-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\d887de99f15dadc70f2a7586cbdfd2e6\transformed\room-runtime-2.6.1\res
com.ramsha_malik.tms_app-androidsvg-aar-1.4-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\db21de1fe14fa7fda874facb8070173c\transformed\androidsvg-aar-1.4\res
com.ramsha_malik.tms_app-core-1.13.1-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\dbf0f811c0b4cd74b2ceefe96db4396e\transformed\core-1.13.1\res
com.ramsha_malik.tms_app-lifecycle-runtime-2.6.2-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\e3ac9d4823f8dbdab4ffb53c2a6f71fa\transformed\lifecycle-runtime-2.6.2\res
com.ramsha_malik.tms_app-annotation-experimental-1.4.0-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\e682df33658d676c6aa49ecaf1fc3319\transformed\annotation-experimental-1.4.0\res
com.ramsha_malik.tms_app-transition-1.5.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\ebad0bc286cf685494bb4dd14790223a\transformed\transition-1.5.0\res
com.ramsha_malik.tms_app-cardview-1.0.0-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\ecfe6108001d232de69077d44b5cc6f6\transformed\cardview-1.0.0\res
com.ramsha_malik.tms_app-sqlite-2.4.0-53 C:\Users\<USER>\.gradle\caches\8.13\transforms\eee174d8b39b4a7f8ebecb54f1fa2903\transformed\sqlite-2.4.0\res
com.ramsha_malik.tms_app-viewpager2-1.0.0-54 C:\Users\<USER>\.gradle\caches\8.13\transforms\fa4da4d9e806d4b321577ae8a396e457\transformed\viewpager2-1.0.0\res
com.ramsha_malik.tms_app-pngs-55 C:\Users\<USER>\Downloads\tms_application\android\app\build\generated\res\pngs\debug
com.ramsha_malik.tms_app-resValues-56 C:\Users\<USER>\Downloads\tms_application\android\app\build\generated\res\resValues\debug
com.ramsha_malik.tms_app-packageDebugResources-57 C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
com.ramsha_malik.tms_app-packageDebugResources-58 C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
com.ramsha_malik.tms_app-debug-59 C:\Users\<USER>\Downloads\tms_application\android\app\build\intermediates\merged_res\debug\mergeDebugResources
com.ramsha_malik.tms_app-debug-60 C:\Users\<USER>\Downloads\tms_application\android\app\src\debug\res
com.ramsha_malik.tms_app-main-61 C:\Users\<USER>\Downloads\tms_application\android\app\src\main\res
com.ramsha_malik.tms_app-debug-62 C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-63 C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\datetimepicker\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-64 C:\Users\<USER>\Downloads\tms_application\node_modules\@react-native-community\netinfo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-65 C:\Users\<USER>\Downloads\tms_application\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-66 C:\Users\<USER>\Downloads\tms_application\node_modules\expo-eas-client\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-67 C:\Users\<USER>\Downloads\tms_application\node_modules\expo-file-system\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-68 C:\Users\<USER>\Downloads\tms_application\node_modules\expo-json-utils\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-69 C:\Users\<USER>\Downloads\tms_application\node_modules\expo-linking\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-70 C:\Users\<USER>\Downloads\tms_application\node_modules\expo-manifests\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-71 C:\Users\<USER>\Downloads\tms_application\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-72 C:\Users\<USER>\Downloads\tms_application\node_modules\expo-structured-headers\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-73 C:\Users\<USER>\Downloads\tms_application\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-74 C:\Users\<USER>\Downloads\tms_application\node_modules\expo-updates\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-75 C:\Users\<USER>\Downloads\tms_application\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-76 C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-edge-to-edge\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-77 C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-78 C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-quick-crypto\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-79 C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-80 C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-81 C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-82 C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-svg\android\build\intermediates\packaged_res\debug\packageDebugResources
com.ramsha_malik.tms_app-debug-83 C:\Users\<USER>\Downloads\tms_application\node_modules\react-native-webview\android\build\intermediates\packaged_res\debug\packageDebugResources
