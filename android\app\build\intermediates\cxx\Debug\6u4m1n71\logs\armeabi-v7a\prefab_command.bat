@echo off
"C:\\Program Files\\JDK_Adoptium_21LTS\\jdk-21.0.6+7\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  armeabi-v7a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging15754155774797976568\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a38de6e1ef4b6972bc76438000ddaf4\\transformed\\react-android-0.79.3-debug\\prefab" ^
  "C:\\Users\\<USER>\\Downloads\\tms_application\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\432cr1w5" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e37fd55cf16f290253b0ce342d71f9db\\transformed\\hermes-android-0.79.3-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aae2e161ad8460d34e386c90489cc2b4\\transformed\\fbjni-0.7.0\\prefab"
