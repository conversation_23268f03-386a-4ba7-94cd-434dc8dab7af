{"logs": [{"outputFile": "com.ramsha_malik.tms_app-mergeDebugResources-57:/values-sr/values-sr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\428bacad40a6f9fbc888386871dcda9e\\transformed\\material-1.12.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,397,473,553,660,753,847,978,1059,1121,1187,1279,1347,1410,1513,1573,1639,1695,1766,1826,1880,1992,2049,2110,2164,2240,2365,2451,2528,2621,2705,2788,2926,3007,3090,3221,3309,3387,3441,3497,3563,3637,3715,3786,3868,3943,4019,4094,4165,4272,4362,4435,4527,4623,4695,4771,4867,4920,5002,5069,5156,5243,5305,5369,5432,5501,5606,5716,5812,5920,5978,6038,6118,6201,6277", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81", "endColumns": "12,75,75,79,106,92,93,130,80,61,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,76,92,83,82,137,80,82,130,87,77,53,55,65,73,77,70,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "316,392,468,548,655,748,842,973,1054,1116,1182,1274,1342,1405,1508,1568,1634,1690,1761,1821,1875,1987,2044,2105,2159,2235,2360,2446,2523,2616,2700,2783,2921,3002,3085,3216,3304,3382,3436,3492,3558,3632,3710,3781,3863,3938,4014,4089,4160,4267,4357,4430,4522,4618,4690,4766,4862,4915,4997,5064,5151,5238,5300,5364,5427,5496,5601,5711,5807,5915,5973,6033,6113,6196,6272,6349"}, "to": {"startLines": "2,35,36,37,38,39,47,48,49,51,52,53,55,58,60,61,62,63,64,65,66,67,68,69,70,71,72,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,127,128,129", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3173,3249,3325,3405,3512,4331,4425,4556,4720,4782,4848,5013,5232,5362,5465,5525,5591,5647,5718,5778,5832,5944,6001,6062,6116,6192,6549,6635,6712,6805,6889,6972,7110,7191,7274,7405,7493,7571,7625,7681,7747,7821,7899,7970,8052,8127,8203,8278,8349,8456,8546,8619,8711,8807,8879,8955,9051,9104,9186,9253,9340,9427,9489,9553,9616,9685,9790,9900,9996,10104,10162,10222,10717,10800,10876", "endLines": "6,35,36,37,38,39,47,48,49,51,52,53,55,58,60,61,62,63,64,65,66,67,68,69,70,71,72,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,127,128,129", "endColumns": "12,75,75,79,106,92,93,130,80,61,65,91,67,62,102,59,65,55,70,59,53,111,56,60,53,75,124,85,76,92,83,82,137,80,82,130,87,77,53,55,65,73,77,70,81,74,75,74,70,106,89,72,91,95,71,75,95,52,81,66,86,86,61,63,62,68,104,109,95,107,57,59,79,82,75,76", "endOffsets": "366,3244,3320,3400,3507,3600,4420,4551,4632,4777,4843,4935,5076,5290,5460,5520,5586,5642,5713,5773,5827,5939,5996,6057,6111,6187,6312,6630,6707,6800,6884,6967,7105,7186,7269,7400,7488,7566,7620,7676,7742,7816,7894,7965,8047,8122,8198,8273,8344,8451,8541,8614,8706,8802,8874,8950,9046,9099,9181,9248,9335,9422,9484,9548,9611,9680,9785,9895,9991,10099,10157,10217,10297,10795,10871,10948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\7bce72c8d6c2b594d45298a87e02b9e4\\transformed\\appcompat-1.7.0\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,212,313,419,505,609,731,815,896,987,1080,1175,1269,1369,1462,1557,1662,1753,1844,1930,2035,2141,2244,2350,2459,2566,2736,2833", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "207,308,414,500,604,726,810,891,982,1075,1170,1264,1364,1457,1552,1657,1748,1839,1925,2030,2136,2239,2345,2454,2561,2731,2828,2915"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,126", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,478,579,685,771,875,997,1081,1162,1253,1346,1441,1535,1635,1728,1823,1928,2019,2110,2196,2301,2407,2510,2616,2725,2832,3002,10630", "endColumns": "106,100,105,85,103,121,83,80,90,92,94,93,99,92,94,104,90,90,85,104,105,102,105,108,106,169,96,86", "endOffsets": "473,574,680,766,870,992,1076,1157,1248,1341,1436,1530,1630,1723,1818,1923,2014,2105,2191,2296,2402,2505,2611,2720,2827,2997,3094,10712"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\5a38de6e1ef4b6972bc76438000ddaf4\\transformed\\react-android-0.79.3-debug\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,212,285,354,436,503,570,652,735,823,906,978,1063,1149,1225,1307,1389,1465,1542,1617,1705,1777,1856,1926", "endColumns": "73,82,72,68,81,66,66,81,82,87,82,71,84,85,75,81,81,75,76,74,87,71,78,69,82", "endOffsets": "124,207,280,349,431,498,565,647,730,818,901,973,1058,1144,1220,1302,1384,1460,1537,1612,1700,1772,1851,1921,2004"}, "to": {"startLines": "34,50,54,56,57,59,73,74,75,122,123,124,125,130,131,132,133,134,135,136,137,139,140,141,142", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3099,4637,4940,5081,5150,5295,6317,6384,6466,10302,10390,10473,10545,10953,11039,11115,11197,11279,11355,11432,11507,11696,11768,11847,11917", "endColumns": "73,82,72,68,81,66,66,81,82,87,82,71,84,85,75,81,81,75,76,74,87,71,78,69,82", "endOffsets": "3168,4715,5008,5145,5227,5357,6379,6461,6544,10385,10468,10540,10625,11034,11110,11192,11274,11350,11427,11502,11590,11763,11842,11912,11995"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\dbf0f811c0b4cd74b2ceefe96db4396e\\transformed\\core-1.13.1\\res\\values-sr\\values-sr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,352,456,560,665,781", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "148,250,347,451,555,660,776,877"}, "to": {"startLines": "40,41,42,43,44,45,46,138", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3605,3703,3805,3902,4006,4110,4215,11595", "endColumns": "97,101,96,103,103,104,115,100", "endOffsets": "3698,3800,3897,4001,4105,4210,4326,11691"}}]}]}